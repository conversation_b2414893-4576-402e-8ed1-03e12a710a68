# KULLM-Pro Configuration
# Production-ready configuration for code switching and fine-tuning

# Model Configuration
model:
  name: "Qwen/Qwen2.5-7B-Instruct"  # Base model for fine-tuning
  max_length: 2048                   # Maximum sequence length
  torch_dtype: "bfloat16"           # Model precision
  device_map: "auto"                # Device mapping strategy
  attn_implementation: "eager"      # Attention implementation

# Training Configuration
training:
  # Basic Training Parameters
  epochs: 3
  batch_size: 2
  learning_rate: 0.0002
  gradient_accumulation_steps: 8
  weight_decay: 0.01
  warmup_ratio: 0.1
  lr_scheduler_type: "cosine"

  # Evaluation and Saving
  eval_steps: 100
  save_steps: 500
  logging_steps: 10
  save_total_limit: 3
  load_best_model_at_end: true
  metric_for_best_model: "eval_loss"

  # Performance Optimizations
  bf16: true
  fp16: false
  gradient_checkpointing: true
  dataloader_num_workers: 0
  optim: "adamw_torch"

  # Advanced Training Options
  remove_unused_columns: false
  ddp_find_unused_parameters: false
  eval_strategy: "steps"
  save_strategy: "steps"
  greater_is_better: false

# LoRA Configuration
lora:
  r: 16                            # LoRA rank
  alpha: 32                        # LoRA alpha parameter
  dropout: 0.1                     # LoRA dropout
  bias: "none"                     # Bias configuration
  task_type: "CAUSAL_LM"          # Task type
  target_modules:                  # Target modules for LoRA
    - "q_proj"
    - "k_proj"
    - "v_proj"
    - "o_proj"
    - "gate_proj"
    - "up_proj"
    - "down_proj"

# Experiment Tracking (Weights & Biases)
wandb:
  project: "kullm-pro"            # WandB project name
  enabled: true                   # Enable/disable WandB logging
  entity: null                    # WandB entity (optional)
  tags: []                        # Additional tags for runs

# Data Configuration
data:
  train_split_ratio: 0.9          # Train/validation split ratio
  val_split_ratio: 0.1            # Validation split ratio (if no separate val file)

# OpenAI Configuration (for code switching)
openai:
  model: "o4-mini-2025-04-16"     # OpenAI model for code switching
  max_tokens: 100000              # Maximum output tokens
  temperature: 1.0                # Sampling temperature
  use_batch_api: true             # Use batch API for cost efficiency
  batch_size: 100                 # Batch size for batch API
  max_concurrent_requests: 10     # Max concurrent requests (regular API)
  requests_per_minute: 1000       # Rate limit for regular API
  max_retries: 3                  # Maximum retry attempts
  timeout: 300                    # Request timeout in seconds
  batch_timeout_hours: 24         # Batch API timeout in hours

# Logging Configuration
logging:
  level: "INFO"                   # Logging level
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: null                      # Optional log file path

# Default Paths (can be overridden by CLI arguments)
paths:
  data_dir: "./data"              # Default data directory
  output_dir: "./outputs"         # Default output directory
  system_prompt: "system_prompt.txt"  # System prompt file for code switching
