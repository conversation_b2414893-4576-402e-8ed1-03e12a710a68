_wandb:
    value:
        cli_version: 0.19.1
        m: []
        python_version: 3.10.0
        t:
            "1":
                - 1
                - 5
                - 11
                - 49
                - 51
                - 53
                - 55
                - 71
                - 98
            "2":
                - 1
                - 5
                - 11
                - 49
                - 51
                - 53
                - 55
                - 71
                - 98
            "3":
                - 13
                - 16
                - 23
                - 55
            "4": 3.10.0
            "5": 0.19.1
            "6": 4.52.4
            "8":
                - 5
            "12": 0.19.1
            "13": linux-x86_64
batch_size:
    value: 2
epochs:
    value: 3
gradient_accumulation_steps:
    value: 8
learning_rate:
    value: 0.0002
lora_alpha:
    value: 32
lora_dropout:
    value: 0.1
lora_r:
    value: 16
max_length:
    value: 2048
model_name:
    value: Qwen/Qwen2.5-7B-Instruct
train_file:
    value: ./data/limo_original_1000_thinking.jsonl
