word2number==1.1
sqlitedict==2.1.0
mpmath==1.3.0
tcolorpy==0.1.6
tabulate==0.9.0
pybind11==2.13.6
portalocker==2.10.1
pathvalidate==3.2.1
mbstrdecoder==1.1.3
jsonlines==4.0.0
rsa==4.9
async-timeout==4.0.3
absl-py==2.1.0
typepy==1.3.2
tqdm-multiprocess==0.0.11
sacrebleu==2.4.3
numexpr==2.10.1
aiosignal==1.3.1
rouge_score==0.1.2
DataProperty==1.0.1
tabledata==1.3.3
accelerate==1.0.0
pytablewriter==1.2.0
peft==0.13.1
evaluate==0.4.3
GitPython==3.1.43
py-cpuinfo==9.0.0
ninja==********
hjson==3.1.0
setproctitle==1.3.3
msgpack==1.1.0
fire==0.7.0
wandb==0.19.1
py==1.11.0
decorator==5.1.1
retry==0.9.2
requests-oauthlib==2.0.0
google-auth==2.36.0
google-auth-oauthlib==1.2.1
gspread==6.1.4
oauth2client==4.1.3
einops==0.8.0
deepspeed==0.16.2
sentencepiece==0.2.0
tensorboard-data-server==0.7.2
smmap==5.0.1
pyarrow-hotfix==0.6
Markdown==3.7
grpcio==1.68.1
docker-pycreds==0.4.0
Werkzeug==3.1.3
tensorboardX==*******
sentry-sdk==2.19.2
nltk==3.8.1
gitdb==4.0.11
tensorboard==2.18.0
py-spy==0.4.0
opencensus-context==0.1.3
nvidia-ml-py==12.570.86
distlib==0.3.9
colorful==0.5.6
blake3==1.0.4
wrapt==1.17.2
websockets==14.2
virtualenv==20.29.2
uvloop==0.21.0
opencensus==0.11.4
rpds-py==0.22.3
pycountry==24.6.1
proto-plus==1.26.0
prometheus_client==0.21.1
partial-json-parser==0.2.1.1.post5
msgspec==0.19.0
lark==1.2.2
jiter==0.8.2
interegular==0.3.3
iniconfig==2.0.0
httptools==0.6.4
googleapis-common-protos==1.67.0
diskcache==5.6.3
cloudpickle==3.1.1
astor==0.8.1
airportsdata==20241001
uvicorn==0.34.0
tiktoken==0.9.0
smart-open==7.1.0
referencing==0.36.2
opencv-python-headless==*********
depyf==0.18.0
watchfiles==1.0.4
jsonschema==4.23.0
starlette==0.45.3
jsonschema-specifications==2024.10.1
google-api-core==2.24.1
prometheus-fastapi-instrumentator==7.0.2
fastapi==0.115.8
aiohttp-cors==0.7.0
outlines_core==0.1.26
outlines==0.1.11
setuptools==78.1.1
wheel==0.45.1
pip==25.1
pytz==2025.2
nvidia-cusparselt-cu12==0.6.3
xxhash==3.5.0
urllib3==2.4.0
tzdata==2025.2
typing_extensions==4.14.0
triton==3.3.1
tqdm==4.67.1
threadpoolctl==3.6.0
tenacity==9.1.2
sympy==1.14.0
sniffio==1.3.1
six==1.17.0
safetensors==0.5.3
regex==2024.11.6
PyYAML==6.0.2
pyparsing==3.2.3
pyarrow==20.0.0
psutil==7.0.0
protobuf==5.29.5
propcache==0.3.2
platformdirs==4.3.8
pillow==11.2.1
packaging==25.0
nvidia-nvtx-cu12==12.6.77
nvidia-nvjitlink-cu12==12.6.85
nvidia-nccl-cu12==2.26.2
nvidia-curand-cu12==*********
nvidia-cufile-cu12==********
nvidia-cuda-runtime-cu12==12.6.77
nvidia-cuda-nvrtc-cu12==12.6.77
nvidia-cuda-cupti-cu12==12.6.80
nvidia-cublas-cu12==********
numpy==2.2.6
networkx==3.4.2
MarkupSafe==3.0.2
kiwisolver==1.4.8
joblib==1.5.1
idna==3.10
hf-xet==1.1.4
h11==0.16.0
fsspec==2025.3.0
frozenlist==1.7.0
fonttools==4.58.4
filelock==3.18.0
distro==1.9.0
dill==0.3.8
cycler==0.12.1
click==8.2.1
charset-normalizer==3.4.2
certifi==2025.6.15
attrs==25.3.0
annotated-types==0.7.0
aiohappyeyeballs==2.6.1
typing-inspection==0.4.1
scipy==1.15.3
requests==2.32.4
python-dateutil==2.9.0.post0
pydantic_core==2.33.2
nvidia-cusparse-cu12==********
nvidia-cufft-cu12==********
nvidia-cudnn-cu12==********
multiprocess==0.70.16
multidict==6.5.0
Jinja2==3.1.6
httpcore==1.0.9
exceptiongroup==1.3.0
contourpy==1.3.2
yarl==1.20.1
scikit-learn==1.7.0
pydantic==2.11.7
pandas==2.3.0
nvidia-cusolver-cu12==********
matplotlib==3.10.3
huggingface-hub==0.33.0
anyio==4.9.0
torch==2.7.1
tokenizers==0.21.1
seaborn==0.13.2
httpx==0.28.1
aiohttp==3.12.13
transformers==4.52.4
openai==1.88.0
bitsandbytes==0.46.0
datasets==3.6.0
