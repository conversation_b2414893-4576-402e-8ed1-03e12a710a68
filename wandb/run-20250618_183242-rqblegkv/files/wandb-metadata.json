{"os": "Linux-6.5.0-34-generic-x86_64-with-glibc2.35", "python": "CPython 3.10.0", "startedAt": "2025-06-18T09:32:42.351857Z", "args": ["--train_file", "./data/limo_original_1000_thinking.jsonl", "--output_dir", "./models/baseline_thinking", "--epochs", "3", "--batch_size", "2"], "program": "/data_x/junkim100/projects/KULLM/KULLM-Pro/train.py", "codePath": "train.py", "git": {"remote": "https://github.com/junkim100/KULLM-Pro.git", "commit": "19ebb463fcd25712f8788cac29d845f8de790b27"}, "email": "<EMAIL>", "root": "/data_x/junkim100/projects/KULLM/KULLM-Pro", "host": "nlp-server-16", "executable": "/mnt/raid6/junkim100/miniconda3/envs/kullm/bin/python", "codePathLocal": "train.py", "cpu_count": 64, "cpu_count_logical": 128, "gpu": "NVIDIA RTX A6000", "gpu_count": 8, "disk": {"/": {"total": "1964618686464", "used": "188829622272"}}, "memory": {"total": "2151664984064"}, "cpu": {"count": 64, "countLogical": 128}, "gpu_nvidia": [{"name": "NVIDIA RTX A6000", "memoryTotal": "51527024640", "cudaCores": 10752, "architecture": "Ampere"}, {"name": "NVIDIA RTX A6000", "memoryTotal": "51527024640", "cudaCores": 10752, "architecture": "Ampere"}, {"name": "NVIDIA RTX A6000", "memoryTotal": "51527024640", "cudaCores": 10752, "architecture": "Ampere"}, {"name": "NVIDIA RTX A6000", "memoryTotal": "51527024640", "cudaCores": 10752, "architecture": "Ampere"}, {"name": "NVIDIA RTX A6000", "memoryTotal": "51527024640", "cudaCores": 10752, "architecture": "Ampere"}, {"name": "NVIDIA RTX A6000", "memoryTotal": "51527024640", "cudaCores": 10752, "architecture": "Ampere"}, {"name": "NVIDIA RTX A6000", "memoryTotal": "51527024640", "cudaCores": 10752, "architecture": "Ampere"}, {"name": "NVIDIA RTX A6000", "memoryTotal": "51527024640", "cudaCores": 10752, "architecture": "Ampere"}], "cudaVersion": "12.3"}