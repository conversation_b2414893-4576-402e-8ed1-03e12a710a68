# KULLM-Pro

**Korean-English Code-Switched Language Model Training Pipeline**

A production-ready framework for creating code-switched datasets and fine-tuning language models with LoRA for Korean-English mathematical reasoning.

## 🚀 Features

- **Flexible Code Switching**: Process any Hugging Face dataset with configurable parameters
- **LoRA Fine-tuning**: Advanced fine-tuning with LoRA, checkpoint management, and experiment tracking
- **OpenAI Integration**: Batch API support for cost-efficient code switching
- **Production Ready**: Proper error handling, logging, and configuration management
- **CLI Interface**: Easy-to-use command-line tools with Python Fire
- **Experiment Tracking**: Weights & Biases integration for monitoring training

## 📋 Table of Contents

- [Installation](#installation)
- [Quick Start](#quick-start)
- [Configuration](#configuration)
- [Code Switching](#code-switching)
- [Fine-tuning](#fine-tuning)
- [Examples](#examples)
- [API Reference](#api-reference)
- [Contributing](#contributing)
- [License](#license)

## 🛠 Installation

### Prerequisites

- Python 3.8 or higher
- CUDA-compatible GPU (recommended for fine-tuning)
- OpenAI API key (for code switching)

### Setup

1. **Clone the repository:**
   ```bash
   git clone https://github.com/your-username/KULLM-Pro.git
   cd KULLM-Pro
   ```

2. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

3. **Set up environment variables:**
   ```bash
   cp .env.example .env
   # Edit .env with your API keys
   ```

4. **Configure the system:**
   ```bash
   # Edit config.yaml with your preferred settings
   vim config.yaml
   ```

## ⚡ Quick Start

### Code Switching

Generate code-switched datasets from any Hugging Face dataset:

```bash
# Basic usage
python code_switch.py --dataset="GAIR/LIMO" --split="train" --n=300

# With custom parameters
python code_switch.py \
  --dataset="microsoft/orca-math-word-problems-200k" \
  --split="train" \
  --subset="default" \
  --n=1000 \
  --output_dir="./data"
```

### Fine-tuning

Train models with LoRA fine-tuning:

```bash
# Basic fine-tuning
python fine_tune.py train \
  --train_file="./data/GAIR_LIMO_train_300_code_switched.jsonl" \
  --output_dir="./outputs/qwen_limo_cs"

# With custom parameters
python fine_tune.py train \
  --train_file="./data/training_data.jsonl" \
  --val_file="./data/validation_data.jsonl" \
  --output_dir="./outputs/my_model" \
  --run_name="experiment_1" \
  --epochs=5 \
  --batch_size=4
```

## ⚙️ Configuration

KULLM-Pro uses a centralized configuration system with `config.yaml`. Key sections include:

### Model Configuration
```yaml
model:
  name: "Qwen/Qwen2.5-7B-Instruct"
  max_length: 2048
  torch_dtype: "bfloat16"
```

### Training Configuration
```yaml
training:
  epochs: 3
  batch_size: 2
  learning_rate: 2e-4
  gradient_accumulation_steps: 8
```

### LoRA Configuration
```yaml
lora:
  r: 16
  alpha: 32
  dropout: 0.1
  target_modules: ["q_proj", "k_proj", "v_proj", "o_proj"]
```

### Environment Variables

Set up your `.env` file with required API keys:

```bash
# Required for code switching
OPENAI_API_KEY=your_openai_api_key_here

# Optional for experiment tracking
WANDB_API_KEY=your_wandb_api_key_here

# Optional for private models/datasets
HF_TOKEN=your_hugging_face_token_here
```
