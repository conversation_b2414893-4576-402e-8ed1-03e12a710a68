#!/usr/bin/env python3
"""
Code Switching CLI

Command-line interface for generating code-switched datasets from Hugging Face datasets.
Uses Python Fire for argument parsing and supports flexible dataset parameters.

Example usage:
    python code_switch.py --dataset="GAIR/LIMO" --split="train" --n=300
    python code_switch.py --dataset="microsoft/orca-math-word-problems-200k" --split="train" --subset="default" --n=1000
"""

import asyncio
import sys
from typing import Optional

import fire
from dotenv import load_dotenv

from kullm_pro.code_switching import CodeSwitchingPipeline, OpenAIConfig
from kullm_pro.utils import setup_logging, load_config

# Load environment variables
load_dotenv()


class CodeSwitchCLI:
    """Command-line interface for code switching"""

    def __init__(self):
        try:
            self.logger = setup_logging()
        except Exception as e:
            print(f"Failed to setup logging: {e}")
            import logging
            self.logger = logging.getLogger(__name__)

    def run(
        self,
        dataset: str,
        split: str = "train",
        subset: Optional[str] = None,
        n: Optional[int] = None,
        output_dir: str = "./data",
        config_file: str = "config.yaml",
        system_prompt: str = "system_prompt.txt",
        use_batch_api: bool = True,
        model: str = "o4-mini-2025-04-16",
        skip_original: bool = False,
        text_column: str = "solution",
        question_column: str = "question",
        solution_column: str = "solution",
        answer_column: str = "answer"
    ):
        """
        Generate code-switched dataset from Hugging Face dataset

        Args:
            dataset: Hugging Face dataset name (e.g., "GAIR/LIMO")
            split: Dataset split to use (default: "train")
            subset: Dataset subset if applicable (default: None)
            n: Number of samples to process (shortest texts, default: None for all)
            output_dir: Output directory for generated files (default: "./data")
            config_file: Path to configuration file (default: "config.yaml")
            system_prompt: Path to system prompt file (default: "system_prompt.txt")
            use_batch_api: Use OpenAI Batch API for cost efficiency (default: True)
            model: OpenAI model to use (default: "o4-mini-2025-04-16")
            skip_original: Skip saving original dataset (default: False)
            text_column: Column name for text length sorting (default: "solution")
            question_column: Column name for questions (default: "question")
            solution_column: Column name for solutions (default: "solution")
            answer_column: Column name for answers (default: "answer")
        """
        self.logger.info("Starting code switching pipeline")
        self.logger.info(f"Dataset: {dataset}")
        self.logger.info(f"Split: {split}")
        self.logger.info(f"Subset: {subset}")
        self.logger.info(f"Samples: {n}")

        # Load configuration
        try:
            config = load_config(config_file)
            self.logger.info(f"Loaded configuration from {config_file}")
        except FileNotFoundError:
            self.logger.error(f"Configuration file not found: {config_file}")
            self.logger.error("Please ensure config.yaml exists or specify a valid config file")
            sys.exit(1)
        except Exception as e:
            self.logger.warning(f"Could not load config file {config_file}: {e}")
            self.logger.warning("Using default configuration")
            config = {}

        # Set up OpenAI configuration
        openai_config_dict = config.get("openai", {})
        # Override with command line arguments
        openai_config_dict.update({
            "model": model,
            "use_batch_api": use_batch_api
        })
        openai_config = OpenAIConfig(**openai_config_dict)

        # Create pipeline
        pipeline = CodeSwitchingPipeline(
            openai_config=openai_config,
            system_prompt_path=system_prompt,
            output_dir=output_dir
        )

        # Run the pipeline
        try:
            original_file, code_switched_file = asyncio.run(
                pipeline.process_dataset(
                    dataset_name=dataset,
                    split=split,
                    subset=subset,
                    n_samples=n,
                    text_column=text_column,
                    question_column=question_column,
                    solution_column=solution_column,
                    answer_column=answer_column,
                    skip_original=skip_original
                )
            )

            self.logger.info("Code switching completed successfully!")
            self.logger.info(f"Original file: {original_file}")
            self.logger.info(f"Code-switched file: {code_switched_file}")

            return {
                "original_file": original_file,
                "code_switched_file": code_switched_file,
                "status": "success"
            }

        except Exception as e:
            self.logger.error(f"Code switching failed: {e}")
            raise

    def batch_process(
        self,
        datasets_config: str,
        **kwargs
    ):
        """
        Process multiple datasets from a configuration file

        Args:
            datasets_config: Path to JSON/YAML file with dataset configurations
            **kwargs: Additional arguments to override for all datasets
        """
        import json
        import yaml
        from pathlib import Path

        config_path = Path(datasets_config)

        # Load datasets configuration
        if config_path.suffix.lower() == '.json':
            with open(config_path, 'r') as f:
                datasets = json.load(f)
        else:
            with open(config_path, 'r') as f:
                datasets = yaml.safe_load(f)

        results = []

        for dataset_config in datasets:
            # Merge with command line overrides
            merged_config = {**dataset_config, **kwargs}

            self.logger.info(f"Processing dataset: {merged_config['dataset']}")

            try:
                result = self.run(**merged_config)
                results.append({
                    "dataset": merged_config['dataset'],
                    "result": result
                })
            except Exception as e:
                self.logger.error(f"Failed to process {merged_config['dataset']}: {e}")
                results.append({
                    "dataset": merged_config['dataset'],
                    "error": str(e)
                })

        return results


def main():
    """Main entry point"""
    fire.Fire(CodeSwitchCLI())


if __name__ == "__main__":
    main()
