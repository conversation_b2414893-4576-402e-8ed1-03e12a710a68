#!/usr/bin/env python3
"""
Test script to verify the training fix works
"""

import json
import tempfile
from pathlib import Path

# Create minimal test data
test_data = [
    {
        "messages": [
            {
                "role": "system",
                "content": "You are a helpful assistant."
            },
            {
                "role": "user", 
                "content": "What is 2+2?"
            },
            {
                "role": "assistant",
                "content": "2+2=4"
            }
        ]
    },
    {
        "messages": [
            {
                "role": "system",
                "content": "You are a helpful assistant."
            },
            {
                "role": "user",
                "content": "What is 3+3?"
            },
            {
                "role": "assistant", 
                "content": "3+3=6"
            }
        ]
    }
]

# Create temporary test file
with tempfile.NamedTemporaryFile(mode='w', suffix='.jsonl', delete=False) as f:
    for item in test_data:
        f.write(json.dumps(item) + '\n')
    test_file = f.name

print(f"Created test file: {test_file}")
print("Test data:")
for i, item in enumerate(test_data):
    print(f"  {i+1}. {item['messages'][1]['content']} -> {item['messages'][2]['content']}")

print("\nTo test the fix, run:")
print(f"python fine_tune.py train \\")
print(f"  --train_file='{test_file}' \\")
print(f"  --output_dir='./test_output' \\")
print(f"  --model_name='gpt2' \\")
print(f"  --epochs=1 \\")
print(f"  --batch_size=1 \\")
print(f"  --learning_rate=0.001")

print(f"\nTest file will be automatically cleaned up when you're done.")
print(f"To clean up manually: rm {test_file}")
