"""
LoRA trainer for fine-tuning language models
"""

import os
import torch
from typing import Dict, Any, Optional
from pathlib import Path

from transformers import (
    AutoModelForCausalLM,
    TrainingArguments,
    Trainer,
    DataCollatorForSeq2Seq,
)
from peft import LoraConfig, get_peft_model, TaskType
from datasets import Dataset
import wandb

from ..utils.logging import get_logger

logger = get_logger("fine_tuning.trainer")

# Fix for compatibility issues
os.environ["TOKENIZERS_PARALLELISM"] = "false"

# Monkey patch for accelerate compatibility
try:
    import accelerate
    if hasattr(accelerate.Accelerator, "unwrap_model"):
        original_unwrap_model = accelerate.Accelerator.unwrap_model

        def patched_unwrap_model(self, model, keep_torch_compile=None):
            if keep_torch_compile is not None:
                # Ignore the keep_torch_compile parameter for older accelerate versions
                return original_unwrap_model(self, model)
            return original_unwrap_model(self, model)

        accelerate.Accelerator.unwrap_model = patched_unwrap_model
except ImportError:
    logger.warning("Accelerate not available, skipping compatibility patch")


class LoRATrainer:
    """LoRA trainer for fine-tuning language models"""

    def __init__(
        self,
        model_name: str,
        tokenizer,
        lora_config: Dict[str, Any],
        training_config: Dict[str, Any],
        wandb_config: Optional[Dict[str, Any]] = None
    ):
        self.model_name = model_name
        self.tokenizer = tokenizer
        self.lora_config = lora_config
        self.training_config = training_config
        self.wandb_config = wandb_config or {}

        self.model = None
        self.trainer = None

        logger.info(f"Initialized LoRA trainer for model: {model_name}")

    def load_model(self) -> AutoModelForCausalLM:
        """
        Load and prepare model with LoRA

        Returns:
            Model with LoRA adapters
        """
        logger.info(f"Loading model: {self.model_name}")

        # Load base model
        model = AutoModelForCausalLM.from_pretrained(
            self.model_name,
            torch_dtype=torch.bfloat16,
            device_map="auto",
            attn_implementation="eager",  # Avoid flash attention issues
            trust_remote_code=True,
        )

        # Setup LoRA
        logger.info("Setting up LoRA configuration...")
        lora_config = LoraConfig(
            r=self.lora_config.get("r", 16),
            lora_alpha=self.lora_config.get("alpha", 32),
            target_modules=self.lora_config.get("target_modules", [
                "q_proj", "k_proj", "v_proj", "o_proj",
                "gate_proj", "up_proj", "down_proj"
            ]),
            lora_dropout=self.lora_config.get("dropout", 0.1),
            bias="none",
            task_type=TaskType.CAUSAL_LM,
        )

        model = get_peft_model(model, lora_config)
        model.print_trainable_parameters()

        # Enable gradient computation for LoRA parameters
        model.train()
        for param in model.parameters():
            if param.requires_grad:
                param.requires_grad_(True)

        self.model = model
        return model

    def setup_training_arguments(self, output_dir: str) -> TrainingArguments:
        """
        Setup training arguments

        Args:
            output_dir: Output directory for model checkpoints

        Returns:
            TrainingArguments instance
        """
        logger.info("Setting up training arguments...")

        # Ensure output directory exists
        Path(output_dir).mkdir(parents=True, exist_ok=True)

        training_args = TrainingArguments(
            output_dir=output_dir,
            num_train_epochs=self.training_config.get("epochs", 3),
            per_device_train_batch_size=self.training_config.get("batch_size", 2),
            per_device_eval_batch_size=self.training_config.get("batch_size", 2),
            gradient_accumulation_steps=self.training_config.get("gradient_accumulation_steps", 8),
            learning_rate=self.training_config.get("learning_rate", 2e-4),
            weight_decay=self.training_config.get("weight_decay", 0.01),
            warmup_ratio=self.training_config.get("warmup_ratio", 0.1),
            lr_scheduler_type=self.training_config.get("lr_scheduler_type", "cosine"),
            logging_steps=self.training_config.get("logging_steps", 10),
            eval_steps=self.training_config.get("eval_steps", 100),
            save_steps=self.training_config.get("save_steps", 500),
            save_total_limit=self.training_config.get("save_total_limit", 3),
            eval_strategy="steps",
            save_strategy="steps",
            load_best_model_at_end=self.training_config.get("load_best_model_at_end", True),
            metric_for_best_model=self.training_config.get("metric_for_best_model", "eval_loss"),
            greater_is_better=False,
            report_to="wandb" if self.wandb_config.get("enabled", False) else "none",
            dataloader_num_workers=self.training_config.get("dataloader_num_workers", 0),
            fp16=False,
            bf16=self.training_config.get("bf16", True),
            gradient_checkpointing=self.training_config.get("gradient_checkpointing", True),
            optim=self.training_config.get("optim", "adamw_torch"),
            remove_unused_columns=False,
            ddp_find_unused_parameters=False,
        )

        return training_args

    def setup_trainer(
        self,
        train_dataset: Dataset,
        val_dataset: Dataset,
        output_dir: str
    ) -> Trainer:
        """
        Setup Hugging Face Trainer

        Args:
            train_dataset: Training dataset
            val_dataset: Validation dataset
            output_dir: Output directory

        Returns:
            Configured Trainer instance
        """
        if self.model is None:
            self.load_model()

        # Setup training arguments
        training_args = self.setup_training_arguments(output_dir)

        # Data collator
        data_collator = DataCollatorForSeq2Seq(
            tokenizer=self.tokenizer,
            model=None,
            label_pad_token_id=-100,
            pad_to_multiple_of=8,
            return_tensors="pt",
            padding=True,
        )

        # Create trainer
        trainer = Trainer(
            model=self.model,
            args=training_args,
            train_dataset=train_dataset,
            eval_dataset=val_dataset,
            data_collator=data_collator,
            tokenizer=self.tokenizer,
        )

        self.trainer = trainer
        return trainer

    def train(
        self,
        train_dataset: Dataset,
        val_dataset: Dataset,
        output_dir: str,
        run_name: Optional[str] = None,
        resume_from_checkpoint: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Train the model

        Args:
            train_dataset: Training dataset
            val_dataset: Validation dataset
            output_dir: Output directory for model
            run_name: WandB run name
            resume_from_checkpoint: Path to checkpoint to resume from

        Returns:
            Training metrics
        """
        logger.info("Starting training...")

        # Initialize WandB if enabled
        if self.wandb_config.get("enabled", False):
            wandb.init(
                project=self.wandb_config.get("project", "kullm-pro-finetuning"),
                name=run_name,
                config={
                    "model_name": self.model_name,
                    "lora_config": self.lora_config,
                    "training_config": self.training_config,
                    "train_samples": len(train_dataset),
                    "val_samples": len(val_dataset),
                },
            )
            logger.info(f"WandB initialized: {self.wandb_config.get('project')}/{run_name}")

        # Setup trainer
        trainer = self.setup_trainer(train_dataset, val_dataset, output_dir)

        try:
            # Train
            if resume_from_checkpoint:
                logger.info(f"Resuming training from checkpoint: {resume_from_checkpoint}")
                trainer.train(resume_from_checkpoint=resume_from_checkpoint)
            else:
                trainer.train()

            # Save model
            logger.info(f"Saving model to {output_dir}")
            trainer.save_model()

            # Save tokenizer
            self.tokenizer.save_pretrained(output_dir)

            # Get final metrics
            final_metrics = {}
            if hasattr(trainer.state, "log_history") and trainer.state.log_history:
                final_metrics = trainer.state.log_history[-1]
                logger.info(f"Final training metrics: {final_metrics}")

            logger.info("Training completed successfully!")
            return final_metrics

        except Exception as e:
            logger.error(f"Training failed: {e}")
            raise
        finally:
            # Clean up WandB
            if self.wandb_config.get("enabled", False):
                wandb.finish()
                logger.info("WandB session finished")

    def evaluate(self, eval_dataset: Dataset) -> Dict[str, Any]:
        """
        Evaluate the model

        Args:
            eval_dataset: Evaluation dataset

        Returns:
            Evaluation metrics
        """
        if self.trainer is None:
            raise ValueError("Trainer not initialized. Call train() first.")

        logger.info("Evaluating model...")
        metrics = self.trainer.evaluate(eval_dataset)
        logger.info(f"Evaluation metrics: {metrics}")

        return metrics
