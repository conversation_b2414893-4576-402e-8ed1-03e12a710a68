"""
KULLM-Pro: Korean-English Code-Switched Language Model Training Pipeline

A production-ready framework for creating code-switched datasets and fine-tuning
language models with LoRA for Korean-English mathematical reasoning.

Components:
- code_switching: Dataset processing and code-switching generation
- fine_tuning: LoRA fine-tuning with advanced training features
- utils: Shared utilities and configuration management
"""

__version__ = "1.0.0"
__author__ = "KULLM-Pro Team"

from .code_switching import CodeSwitchingPipeline
from .fine_tuning import FineTuningPipeline
from .utils import load_config, setup_logging

__all__ = [
    "CodeSwitchingPipeline",
    "FineTuningPipeline", 
    "load_config",
    "setup_logging",
]
