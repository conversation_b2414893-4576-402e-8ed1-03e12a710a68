"""
OpenAI API client for code-switching data processing
"""

import os
import asyncio
import logging
import time
import tempfile
import uuid
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
import json

import openai
from openai import AsyncOpenAI, OpenAI
from tenacity import (
    retry,
    stop_after_attempt,
    wait_exponential,
    retry_if_exception_type,
)
import aiohttp

from ..utils.logging import get_logger

logger = get_logger("code_switching.openai_client")


@dataclass
class OpenAIConfig:
    """Configuration for OpenAI API"""

    api_key: Optional[str] = None
    model: str = "o4-mini-2025-04-16"
    max_tokens: int = 100000
    temperature: float = 1.0
    use_batch_api: bool = True
    batch_size: int = 100
    max_concurrent_requests: int = 10
    requests_per_minute: int = 1000
    max_retries: int = 3
    retry_delay: float = 1.0
    timeout: int = 300
    batch_timeout_hours: int = 24

    def __post_init__(self):
        if self.api_key is None:
            self.api_key = os.getenv("OPENAI_API_KEY")
        if not self.api_key:
            raise ValueError(
                "OpenAI API key not found. Set OPENAI_API_KEY environment variable."
            )


class OpenAIClient:
    """Async OpenAI client with rate limiting and error handling"""

    def __init__(self, config: OpenAIConfig):
        self.config = config
        self.client = AsyncOpenAI(api_key=config.api_key)
        self.sync_client = OpenAI(api_key=config.api_key)

        # Set up rate limiting
        self.semaphore = asyncio.Semaphore(config.max_concurrent_requests)

        # Track usage
        self.total_requests = 0
        self.successful_requests = 0
        self.failed_requests = 0
        self.total_tokens_used = 0

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10),
        retry=retry_if_exception_type(
            (openai.RateLimitError, openai.APITimeoutError, aiohttp.ClientError)
        ),
    )
    async def _make_request(self, messages: List[Dict[str, str]]) -> Dict[str, Any]:
        """Make a single API request with retry logic"""

        async with self.semaphore:
            try:
                self.total_requests += 1

                response = await self.client.chat.completions.create(
                    model=self.config.model,
                    messages=messages,
                    max_completion_tokens=self.config.max_tokens,
                    temperature=self.config.temperature,
                    timeout=self.config.timeout,
                )

                self.successful_requests += 1

                # Track token usage
                if hasattr(response, "usage") and response.usage:
                    self.total_tokens_used += response.usage.total_tokens

                return {
                    "success": True,
                    "response": response.choices[0].message.content,
                    "usage": (response.usage.model_dump() if response.usage else None),
                    "model": response.model,
                }

            except openai.RateLimitError as e:
                logger.warning(f"Rate limit hit: {e}")
                self.failed_requests += 1
                raise

            except openai.APITimeoutError as e:
                logger.warning(f"API timeout: {e}")
                self.failed_requests += 1
                raise

            except Exception as e:
                logger.error(f"API request failed: {e}")
                self.failed_requests += 1
                return {"success": False, "error": str(e), "response": None}

    async def process_single_item(
        self, system_prompt: str, user_content: str
    ) -> Dict[str, Any]:
        """Process a single item through the API"""

        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": f"Input: {user_content}"},
        ]

        result = await self._make_request(messages)
        return result

    async def process_batch(
        self,
        system_prompt: str,
        items: List[str],
        progress_callback: Optional[callable] = None,
    ) -> List[Dict[str, Any]]:
        """Process a batch of items"""

        logger.info(f"Processing batch of {len(items)} items")

        # Create tasks for all items
        tasks = []
        for i, item in enumerate(items):
            task = self.process_single_item(system_prompt, item)
            tasks.append(task)

        # Process with progress tracking
        results = []
        for i, task in enumerate(asyncio.as_completed(tasks)):
            result = await task
            results.append(result)

            if progress_callback:
                progress_callback(i + 1, len(items))

            # Log progress every 10 items
            if (i + 1) % 10 == 0:
                logger.info(f"Processed {i + 1}/{len(items)} items")

        return results

    def create_batch_file(self, system_prompt: str, items: List[str]) -> str:
        """Create a batch file for OpenAI Batch API"""
        batch_requests = []

        for i, item in enumerate(items):
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": f"Input: {item}"},
            ]

            request = {
                "custom_id": f"request-{i}",
                "method": "POST",
                "url": "/v1/chat/completions",
                "body": {
                    "model": self.config.model,
                    "messages": messages,
                    "max_completion_tokens": self.config.max_tokens,
                    "temperature": self.config.temperature,
                },
            }
            batch_requests.append(request)

        # Create temporary file
        with tempfile.NamedTemporaryFile(mode="w", suffix=".jsonl", delete=False) as f:
            for request in batch_requests:
                f.write(json.dumps(request) + "\n")
            return f.name

    def submit_batch(self, batch_file_path: str) -> str:
        """Submit batch file to OpenAI Batch API"""
        logger.info(f"Submitting batch file: {batch_file_path}")

        with open(batch_file_path, "rb") as f:
            batch_input_file = self.sync_client.files.create(file=f, purpose="batch")

        batch = self.sync_client.batches.create(
            input_file_id=batch_input_file.id,
            endpoint="/v1/chat/completions",
            completion_window="24h",
        )

        logger.info(f"Batch submitted with ID: {batch.id}")
        return batch.id

    def wait_for_batch(self, batch_id: str, check_interval: int = 60) -> List[Dict[str, Any]]:
        """Wait for batch to complete and return results"""
        logger.info(f"Waiting for batch {batch_id} to complete...")

        while True:
            batch = self.sync_client.batches.retrieve(batch_id)
            logger.info(f"Batch status: {batch.status}")

            if batch.status == "completed":
                logger.info("Batch completed successfully!")
                return self.process_batch_results(batch.output_file_id)
            elif batch.status == "failed":
                logger.error("Batch failed!")
                raise Exception(f"Batch {batch_id} failed")
            elif batch.status in ["cancelled", "expired"]:
                logger.error(f"Batch {batch.status}")
                raise Exception(f"Batch {batch_id} was {batch.status}")

            time.sleep(check_interval)

    def process_batch_results(self, output_file_id: str) -> List[Dict[str, Any]]:
        """Process batch results from output file"""
        logger.info(f"Processing batch results from file: {output_file_id}")

        # Download results
        file_response = self.sync_client.files.content(output_file_id)
        results = []

        for line in file_response.text.strip().split("\n"):
            if line:
                result = json.loads(line)
                if result.get("response"):
                    response_data = result["response"]["body"]
                    if response_data.get("choices"):
                        content = response_data["choices"][0]["message"]["content"]
                        usage = response_data.get("usage", {})

                        # Track usage
                        if usage.get("total_tokens"):
                            self.total_tokens_used += usage["total_tokens"]

                        results.append(
                            {
                                "success": True,
                                "response": content,
                                "usage": usage,
                                "model": response_data.get("model"),
                                "custom_id": result.get("custom_id"),
                            }
                        )
                    else:
                        results.append(
                            {
                                "success": False,
                                "error": "No choices in response",
                                "custom_id": result.get("custom_id"),
                            }
                        )
                else:
                    error_msg = result.get("error", {}).get("message", "Unknown error")
                    results.append(
                        {
                            "success": False,
                            "error": error_msg,
                            "custom_id": result.get("custom_id"),
                        }
                    )

        # Sort by custom_id to maintain order
        results.sort(key=lambda x: int(x.get("custom_id", "request-0").split("-")[1]))

        self.successful_requests += len([r for r in results if r["success"]])
        self.failed_requests += len([r for r in results if not r["success"]])
        self.total_requests += len(results)

        return results

    def get_usage_stats(self) -> Dict[str, Any]:
        """Get usage statistics"""
        return {
            "total_requests": self.total_requests,
            "successful_requests": self.successful_requests,
            "failed_requests": self.failed_requests,
            "success_rate": self.successful_requests / max(self.total_requests, 1),
            "total_tokens_used": self.total_tokens_used,
            "estimated_cost": self.estimate_cost(),
        }

    def estimate_cost(self) -> float:
        """Estimate cost based on token usage"""
        # o4-mini batch pricing: $0.55 per 1M input tokens, $2.20 per 1M output tokens
        # For simplicity, we'll estimate average cost (assuming ~20% input, 80% output)
        estimated_cost_per_1k_tokens = (
            0.00287  # Rough average: (0.55*0.2 + 2.20*0.8)/1000
        )
        return (self.total_tokens_used / 1000) * estimated_cost_per_1k_tokens
